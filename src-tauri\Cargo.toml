[package]
name = "wp-installer-herd"
version = "0.1.0"
description = "WPHerd - WordPress Setup in Single Command"
authors = ["Obayed Mamur <<EMAIL>>"]
homepage = "https://obayedmamur.com"
repository = "https://github.com/ObayedMamur/wp-installer-herd"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "wp_installer_herd_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"

