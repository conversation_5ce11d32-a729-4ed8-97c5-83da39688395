{"$schema": "https://schema.tauri.app/config/2", "productName": "WPHerd", "version": "1.0.1", "identifier": "com.obayedmamur.wpherd", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "WPHerd", "width": 1400, "height": 900, "minWidth": 1200, "minHeight": 800, "center": true, "resizable": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}